@extends('layout.homepage.master')
@section('custom-header')
    <title>{{empty($page) ? $detail->title . " | Hiệp Khách <PERSON>iang <PERSON>ồ - Yulgang Origin VTC" : "Cẩm nang | Hiệp Khách Giang Hồ - Yulgang Origin VTC"}}</title>
    <meta name="keywords" content="{{keywords()}}">
    <meta name="description" content="{{empty($page) ? $detail->title . " | Hiệp Khách Giang Hồ - Yulgang Origin VTC, tựa game kiếm hiệp huyền thoại, đã trở lại với các hệ phái mới và các sự kiện độc quyền. Tải và chơi ngay hôm nay." : "Cẩm nang | Hiệp Khách Giang Hồ - Yulgang Origin VTC, tựa game kiếm hiệp huyền thoại, đã trở lại với các hệ phái mới và các sự kiện độc quyền. Tải và chơi ngay hôm nay."}}" />
    <meta property="og:locale" content="vi_VN" />
    <meta property="og:site_name" content="Hiệp Khách Giang Hồ - Yulgang Origin VTC" />
    <meta property="og:title" content="{{empty($page) ? $detail->title . " | Hiệp Khách Giang Hồ - Yulgang Origin VTC" : "Cẩm nang | Hiệp Khách Giang Hồ - Yulgang Origin VTC"}}" />
    <meta property="og:image" content="{{asset('./assets/homepage/images/meta-img.webp')}}" />
    <meta property="og:url" content="{{ url()->current() }}" />
    <meta property="og:type" content="website" />
@endsection
@section('main-body')
    <div class="wrapper">
        <div class="page-tt">
            <section id="frame7" class="section section-frame7">
                <div class="section-background">
                    <img src="{{asset('./assets/homepage/images/bg-header-pagett.jpg')}}" class="img-fluid" alt="" />
                </div>
                <div class="section-content">

                </div>
            </section>
            <section id="frame8" class="section section-frame8">
                <div class="section-background">
                    <img src="{{asset('./assets/homepage/images/bg-body-pagett.jpg')}}" class="img-fluid" alt="" />
                </div>
                <div class="section-content">
                    <div class="d-flex justify-content-center">
                        <div class="nav-left">
                            <div class="btn-taigame">
                                <a href="{{ overrideTrackingDownloadUrl('https://dl.splay.vn/download-game') }}" title="Tải game"><img src="{{asset('./assets/homepage/images/btn-taigame-xl.png')}}" class="img-fluid"
                                                                  alt="" /></a>
                            </div>
                            <div class="nav-group">
                                @foreach($categories as $k => $item)
                                    <div class="nav-item">
                                        <div class="dropdown text-center mx-auto">
                                            <button type="button" class="btn dropdown-toggle" data-bs-toggle="dropdown">
                                                {{$item->name}}
                                            </button>
                                            <ul class="dropdown-menu">
                                                @foreach($item['post'] as $i => $post)
                                                    <li><a class="dropdown-item" href="{{route('cam_nang_detail', $post->title_domain)}}">{{$post->title}}</a></li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <div class="top text-center">
                                <a href="#" title=""><img src="images/btn-top.png" class="img-fluid"
                                                          alt="" /></a>
                            </div>
                        </div>
                        <div class="main-content">
                            <div class="tabs-top">
                                <ul class="nav justify-content-between">
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{route('tin_tuc')}}">Tin tức</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{route('su_kien')}}">Sự kiện</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link active" href="#">Cẩm nang</a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" href="{{route('huong_dan')}}">Hướng dẫn</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="tintuc-sukien">
                                @include('cam-nang.content')
                            </div>
                        </div>
                    </div>
                </div>
            </section>




        </div>
        <div class="footer">
            <div class="container">

                <div class="row align-items-center">
                    <h2 class=" text-center col-12"><img src="{{asset('./assets/homepage/images/logo-vtcmobile.png')}}" class="img-fluid" alt="" />
                        <img src="{{asset('./assets/homepage/images/sep.png')}}" class="img-fluid" alt="" />
                        <img src="{{asset('./assets/homepage/images/logo-mgame.png')}}" class="img-fluid" alt="" />
                    </h2>
                    <div class="col-12 text-center text-footer">
                        <p class="mb-0">Công ty cổ phần VTC dịch vụ di động - Tầng 11 - Tòa nhà VTC Online, số 18 Tam
                            Trinh phường
                            Minh Khai, quận Hai Bà Trưng, Hà Nội</p>
                        <p class="mb-0">SĐT: (84-4).39877470 Email: <EMAIL></p>
                        <p class="mb-0">Giấy phép phê duyệt nội dung kịch bản trò chơi điện tử trên mạng số 1595/QĐ-BTTTT
                        </p>
                        <p class="mb-0">Người chịu trách nhiệm quản lý nội dung: Ông Nguyễn Viết Quang Minh</p>
                        <p class="mb-0 text-info">
                            <a class="text-info text-decoration-none" href="https://hiepkhach.vtczone.vn/huong-dan/chinh-sach-bao-mat-thong-tin-88.html" title="Chính sách bảo mật">
                                Chính sách bảo mật
                            </a>
                            -
                            <a class="text-info text-decoration-none" href="https://hiepkhach.vtczone.vn/huong-dan/huong-dan-tai-game-50.html" title="Hướng dẫn">
                                Hướng dẫn cài
                                đặt
                                và gỡ bỏ
                            </a>
                            -
                            <a class="text-info text-decoration-none" href="https://hiepkhach.vtczone.vn/huong-dan/dieu-khoan-su-dung-89.html" title="Điều khoản">
                                Điều
                                khoản
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@section('script')
    <script>
        function event_popup_open(popupId, url, width, height) {
            // Sửa lại cú pháp trong window.open
            const popupWindow = window.open(url, popupId, `width=${width},height=${height},top=${(window.innerHeight - height) / 2},left=${(window.innerWidth - width) / 2}`);
            if (popupWindow) {
                popupWindow.focus();
            } else {
                alert("Popup bị chặn! Vui lòng cho phép popups trong trình duyệt của bạn.");
            }
        }

        function handleLinkClick(event) {
            const url = event.currentTarget.href;
            const urlParams = new URLSearchParams(url.split('?')[1]);

            // Kiểm tra xem URL có chứa các tham số cần thiết hay không
            if (urlParams.has('cate') && urlParams.has('subcate') && urlParams.has('idx')) {
                event.preventDefault(); // Ngăn chặn hành vi mặc định
                event_popup_open('popup_guide', url, '960', '705'); // Mở popup
            }
        }

        document.addEventListener('DOMContentLoaded', function () {
            // Tìm tất cả các liên kết trên trang
            const links = document.querySelectorAll('a');

            // Thêm sự kiện click cho từng liên kết
            links.forEach(link => {
                link.addEventListener('click', handleLinkClick);
            });
        });
    </script>
@endsection
