<!DOCTYPE html>
<html lang="en">
<head>
  <title>CSS Styling Example | Jcrop Demo</title>
  <meta http-equiv="Content-type" content="text/html;charset=UTF-8" />

<script src="../js/jquery.min.js"></script>
<script src="../js/jquery.Jcrop.js"></script>
<script src="../js/jquery.color.js"></script>
<script type="text/javascript">
  jQuery(function($){

    var api;

    $('#target').Jcrop({
      // start off with jcrop-light class
      bgOpacity: 0.5,
      bgColor: 'white',
      addClass: 'jcrop-light'
    },function(){
      api = this;
      api.setSelect([130,65,130+350,65+285]);
      api.setOptions({ bgFade: true });
      api.ui.selection.addClass('jcrop-selection');
    });

    $('#buttonbar').on('click','button',function(e){
      var $t = $(this), $g = $t.closest('.btn-group');
      $g.find('button.active').removeClass('active');
      $t.addClass('active');
      $g.find('[data-setclass]').each(function(){
        var $th = $(this), c = $th.data('setclass'),
          a = $th.hasClass('active');
        if (a) {
          api.ui.holder.addClass(c);
          switch(c){

            case 'jcrop-light':
              api.setOptions({ bgColor: 'white', bgOpacity: 0.5 });
              break;

            case 'jcrop-dark':
              api.setOptions({ bgColor: 'black', bgOpacity: 0.4 });
              break;

            case 'jcrop-normal':
              api.setOptions({
                bgColor: $.Jcrop.defaults.bgColor,
                bgOpacity: $.Jcrop.defaults.bgOpacity
              });
              break;
          }
        }
        else api.ui.holder.removeClass(c);
      });
    });

  });



</script>
<link rel="stylesheet" href="demo_files/main.css" type="text/css" />
<link rel="stylesheet" href="demo_files/demos.css" type="text/css" />
<link rel="stylesheet" href="../css/jquery.Jcrop.css" type="text/css" />

</head>
<body>

<div class="container">
<div class="row">
<div class="span12">
<div class="jc-demo-box">

<div class="page-header">
<ul class="breadcrumb first">
  <li><a href="../index.html">Jcrop</a> <span class="divider">/</span></li>
  <li><a href="../index.html">Demos</a> <span class="divider">/</span></li>
  <li class="active">CSS Styling Example</li>
</ul>
<h1>CSS Styling Example</h1>
</div>

  <img src="demo_files/sago.jpg" id="target" alt="[Jcrop Example]" />

  <div style="margin-top:1em; width:500px;">
  <fieldset>
  <legend>Manipulate classes</legend>
    <div class="btn-group" id="buttonbar">
      <button id="radio1" data-setclass="jcrop-light" class="btn active" />jcrop-light</button>
      <button id="radio2" data-setclass="jcrop-dark" class="btn" />jcrop-dark</button>
      <button id="radio3" data-setclass="jcrop-normal" class="btn" />normal</button>
    </div>
  </fieldset>
  </div>

  <p>
    <b>Example styling tricks.</b> Click the buttons above to change the appearance of Jcrop in real-time.
  </p>


<div class="tapmodo-footer">
  <a href="http://tapmodo.com" class="tapmodo-logo segment">tapmodo.com</a>
  <div class="segment"><b>&copy; 2008-2013 Tapmodo Interactive LLC</b><br />
    Jcrop is free software released under <a href="../MIT-LICENSE.txt">MIT License</a>
  </div>
</div>

<div class="clearfix"></div>

</div>
</div>
</div>
</div>

</body>
</html>

