<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
<head>
    <meta charset="utf-8">
    <title>Bootstrap switch - by <PERSON><PERSON> and <PERSON></title>
    <meta name="description" content="Switches for radio buttons and checkboxes">
    <meta name="author" content="Matt<PERSON> Larentis">
    <meta name="subauthor" content="Peter Stein">
    <meta http-equiv="X-UA-Compatible" content="IE=9; IE=8;" />

    <link rel="stylesheet" href="http://netdna.bootstrapcdn.com/twitter-bootstrap/2.3.2/css/bootstrap-combined.min.css" />
    <link rel="stylesheet" href="../static/stylesheets/bootstrap-switch.css" />
    <link rel="stylesheet" href="http://getbootstrap.com/2.3.2/assets/js/google-code-prettify/prettify.css" />
    <link rel="stylesheet" href="http://getbootstrap.com/2.3.2/assets/css/docs.css" />
    <link rel="stylesheet" href="http://netdna.bootstrapcdn.com/font-awesome/3.2.1/css/font-awesome.css">
    <link rel="stylesheet" href="http://bdmdesign.github.io/bootstrap-switch/static/stylesheets/flat-ui-fonts.css">

    <style type="text/css">
        body {
            padding-top: 60px;
            padding-bottom: 40px;
          }
        .span4, h1, h4 {
            text-align: center;
        }

        h1 {
            font-size: 4em;
        }

        h4 {
            font-weight: 200;
        }

        h1 {
            margin-bottom: 20px;
        }

        h3 {
            margin-top: 20px;
        }
    </style>

    <script>
        var _gaq = _gaq || [];
        _gaq.push(['_setAccount', 'UA-********-1']);
        _gaq.push(['_trackPageview']);

        (function () {
            var ga = document.createElement('script');
            ga.type = 'text/javascript';
            ga.async = true;
            ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
            var s = document.getElementsByTagName('script')[0];
            s.parentNode.insertBefore(ga, s);
        })();

    </script>

</head>
<body>
    <a href="https://github.com/nostalgiaz/bootstrap-switch">
        <img style="position: absolute; top: 0; right: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_right_red_aa0000.png" alt="Fork me on GitHub">
    </a>

    <div class="container">
        <div class="row-fluid">
            <div class="span8 offset2">
                <h1>Bootstrap switch</h1>
                <h4>
                    by
                    <a href="http://larentis.eu" target="_blank">Mattia Larentis</a> (<a href="https://twitter.com/spiritualguru">@SpiritualGuru</a>)
                    and
                    <a href="http://www.bdmdesign.org/" target="_blank">Peter Stein</a>
                </h4>
                <br>
            </div>
        </div>

        <div class="row-fluid">
            <div class="span8 offset2" style="text-align: center;">
                <iframe src="http://ghbtns.com/github-btn.html?user=nostalgiaz&repo=bootstrap-switch&type=watch&count=true"
                        allowtransparency="true" frameborder="0" scrolling="0" width="170" height="30"></iframe>
                <iframe src="http://ghbtns.com/github-btn.html?user=nostalgiaz&repo=bootstrap-switch&type=fork&count=true"
                        allowtransparency="true" frameborder="0" scrolling="0" width="170" height="30"></iframe>
                <iframe src="http://ghbtns.com/github-btn.html?user=nostalgiaz&type=follow&count=true"
                        allowtransparency="true" frameborder="0" scrolling="0" width="170" height="30"></iframe>
                <iframe src="http://ghbtns.com/github-btn.html?user=BdMdesigN&type=follow&count=true"
                        allowtransparency="true" frameborder="0" scrolling="0" width="170" height="30"></iframe>
            </div>
        </div>

        <div class="row-fluid">
            <h2 class="span8 offset2">
                <a name="size" class="anchor" href="#size">Size</a>
            </h2>
        </div>

        <div class="row-fluid">
            <div class="span8 offset2">
                <div class="bs-docs-example">
                    <div class="make-switch switch-large">
                        <input type="checkbox" checked>
                    </div>

                    <div class="make-switch">
                        <input type="checkbox" checked>
                    </div>

                    <div class="make-switch switch-small">
                        <input type="checkbox" checked>
                    </div>

                    <div class="make-switch switch-mini">
                        <input type="checkbox" checked>
                    </div>

                    <br>
                    <br>

                    <span>Dimensions can be changed too:</span>
                    <div id="dimension-switch" class="make-switch">
                        <input type="checkbox" checked>
                    </div>

                    <br />
                    <br />

                    <button id="btn-size-large-switch" class="btn">Large</button>
                    <button id="btn-size-regular-switch" class="btn">Regular</button>
                    <button id="btn-size-small-switch" class="btn">Small</button>
                    <button id="btn-size-mini-switch" class="btn">Mini</button>
                </div>
            <pre class="prettyprint linenums">
&lt;div class="make-switch switch-large">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch switch-small">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch switch-mini">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div id="dimension-switch" class="make-switch">
    &lt;input type="checkbox" checked>
&lt;/div></pre>
            <pre class="prettyprint linenums">
// Resets to the regular style
$('#dimension-switch').bootstrapSwitch('setSizeClass', '');
// Sets a mini switch
$('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-mini');
// Sets a small switch
$('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-small');
// Sets a large switch
$('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-large');</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="colors" class="anchor" href="#colors">Colors</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="make-switch" data-on="primary" data-off="info">
                    <input type="checkbox" checked>
                </div>
                <div class="make-switch" data-on="info" data-off="success">
                    <input type="checkbox" checked>
                </div>
                <div class="make-switch" data-on="success" data-off="warning">
                    <input type="checkbox" checked>
                </div>
                <div class="make-switch" data-on="warning" data-off="danger">
                    <input type="checkbox" checked>
                </div>
                <div class="make-switch" data-on="danger" data-off="default">
                    <input type="checkbox" checked>
                </div>
                <div class="make-switch" data-on="default" data-off="primary">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <span>Colors can be changed too:</span>
                <div id="change-color-switch" class="make-switch" data-on="default" data-off="primary">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <button id="btn-color-on-switch" class="btn btn-success">
                    Change "On" color
                </button>
                <button id="btn-color-off-switch" class="btn btn-danger">
                    Change "Off" color
                </button>
            </div>
            
            <pre class="prettyprint linenums">
&lt;div class="make-switch" data-on="primary" data-off="info">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-on="info" data-off="success">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-on="success" data-off="warning">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-on="warning" data-off="danger">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-on="danger" data-off="default">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-on="default" data-off="primary">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div id="change-color-switch" class="make-switch" data-on="default" data-off="primary">
    &lt;input type="checkbox" checked>
&lt;/div></pre>
            
            <pre class="prettyprint linenums">
$('#change-color-switch').bootstrapSwitch('setOnClass', 'success');
$('#change-color-switch').bootstrapSwitch('setOffClass', 'danger');</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="animation" class="anchor" href="#animation">Animation <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div id="animated-switch" class="make-switch" data-animated="false">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <button id="btn-animate-switch" class="btn">Animate</button>
                <button id="btn-dont-animate-switch" class="btn">Don't animate</button>
            </div>
            <pre class="prettyprint linenums">
&lt;div id="animated-switch" class="make-switch" data-animated="false">
    &lt;input type="checkbox" checked>
&lt;/div></pre>

            <pre class="prettyprint linenums">
// Enables animation for the selected item
$('#animated-switch').bootstrapSwitch('setAnimated', true);
// Disables animation for the selected item
$('#animated-switch').bootstrapSwitch('setAnimated', false);</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="disabled" class="anchor" href="#disabled">Disabled</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="make-switch">
                    <input type="checkbox" checked disabled>
                </div>
            </div>
            <pre class="prettyprint linenums">
&lt;div class="make-switch">
   &lt;input type="checkbox" checked disabled>
&lt;/div></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="text" class="anchor" href="#text">Text</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div id="label-switch" class="make-switch" data-on-label="SI" data-off-label="NO">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <button id="btn-label-on-switch" class="btn">Change "On" label</button>
                <button id="btn-label-off-switch" class="btn">Change "Off" label</button>

            </div>
            <pre class="prettyprint linenums">
&lt;div id="label-switch" class="make-switch" data-on-label="SI" data-off-label="NO">
    &lt;input type="checkbox" checked>
&lt;/div></pre>
            <pre class="prettyprint linenums">
$('#label-switch').bootstrapSwitch('setOnLabel', 'I');
$('#label-switch').bootstrapSwitch('setOffLabel', 'O');</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="label-text" class="anchor" href="#label-text">Label Text</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="make-switch" data-text-label="TV">
                    <input type="checkbox" checked>
                </div>
            </div>
            <pre class="prettyprint linenums">
&lt;div class="make-switch" data-text-label="TV">
    &lt;input type="checkbox" checked>
&lt;/div></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="html-text" class="anchor" href="#html-text">HTML text</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="make-switch" data-on-label="<i class='icon-ok icon-white'></i>" data-off-label="<i class='icon-remove'></i>">
                    <input type="checkbox" checked>
                </div>
            </div>
            <pre class="prettyprint linenums">
&lt;div class="make-switch" data-on-label="&lt;i class='icon-ok icon-white'>&lt;/i>" data-off-label="&lt;i class='icon-remove'>&lt;/i>">
    &lt;input type="checkbox" checked />
&lt;/div></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="html-text-label-icon" class="anchor" href="#html-text-label-icon">HTML text Label Icon</a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <h5>Standard</h5>
                <div class="make-switch switch-large" data-label-icon="icon-fullscreen" data-on-label="<i class='icon-ok icon-white'></i>" data-off-label="<i class='icon-remove'></i>">
                    <input type="checkbox" checked>
                </div>

                <h5>Font Awesome</h5>
                <div class="make-switch switch-large" data-label-icon="icon-youtube icon-large" data-on-label="<i class='icon-thumbs-up icon-white'></i>" data-off-label="<i class='icon-thumbs-down'></i>">
                    <input type="checkbox" checked>
                </div>

                <h5>Flat UI</h5>
                <div class="make-switch switch-large" data-label-icon="fui-video" data-on-label="<i class='fui-check icon-white'></i>" data-off-label="<i class='fui-cross'></i>">
                    <input type="checkbox" checked>
                </div>
            </div>

            <!--<h3>Standard</h3>-->
            <pre class="prettyprint linenums">
&lt;div class="make-switch switch-large" data-label-icon="icon-fullscreen" data-on-label="&lt;i class='icon-ok icon-white'>&lt;/i>" data-off-label="&lt;i class='icon-remove'>&lt;/i>">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch switch-large" data-label-icon="icon-youtube icon-large" data-on-label="&lt;i class='icon-thumbs-up icon-white'>&lt;/i>" data-off-label="&lt;i class='icon-thumbs-down'>&lt;/i>">
    &lt;input type="checkbox" checked>
&lt;/div>

&lt;div class="make-switch" data-label-icon="fui-video" data-on-label="&lt;i class='fui-check icon-white'>&lt;/i>" data-off-label="&lt;i class='fui-cross'>&lt;/i>">
    &lt;input type="checkbox" checked>
&lt;/div></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="event-handler-javascript" class="anchor" href="#event-handler-javascript">Event handler <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div id="mySwitch" class="make-switch">
                    <input type="checkbox" checked>
                </div>
            </div>
            <pre class="prettyprint linenums">
$('#mySwitch').on('switch-change', function (e, data) {
    var $el = $(data.el)
      , value = data.value;
    console.log(e, $el, value);
});</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="label-event-handler-javascript" class="anchor" href="#label-event-handler-javascript">Label Event handler <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <h5>Label 1</h5>
                <label id="label-toggle-switch">Click on this Text to change the switch state</label>
                <div class="label-toggle-switch make-switch">
                    <input type="checkbox" checked>
                </div>

                <h5>Label 2</h5>
                <div id="label2-toggle-switch">
                    <label class="label-change-switch">Click on this Text to change the switch state
                        <div class="label2-toggle-switch make-switch">
                            <input type="checkbox" checked>
                        </div>
                    </label>
                </div>
            </div>

            <pre class="prettyprint linenums">
&lt;label id="label-toggle-switch">Click on this Text to change the switch state&lt;/label>
    &lt;div class="label-toggle-switch make-switch">
        &lt;input type="checkbox" checked />
    &lt;/div>
&lt;script>
    $('#label-toggle-switch').on('click', function(e, data) {
        $('.label-toggle-switch').bootstrapSwitch('toggleState');
    });
    $('.label-toggle-switch').on('switch-change', function (e, data) {
        alert(data.value);
    });
&lt;/script>

&lt;div id="label2-toggle-switch">
    &lt;label class="label-change-switch">Click on this Text to change the switch state
    &lt;div class="label2-toggle-switch make-switch">
        &lt;input type="checkbox" checked />
    &lt;/div>
    &lt;/label>
&lt;/div>
&lt;script>
    $('#label2-toggle-switch').on('switch-change', function(e, data) {
        alert(data.value);
    });
&lt;/script></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="toggle-state" class="anchor" href="#toggle-state">Toggle State <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div id="toggle-state-switch" class="make-switch">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <div id="toggle-state-switch-button-status" class="btny">Status!</div>
                <div id="toggle-state-switch-button-on" class="btn">ON!</div>
                <div id="toggle-state-switch-button" class="btn">Toggle me!</div>
                <div id="toggle-state-switch-button-off" class="btn">OFF!</div>
            </div>
            <pre class="prettyprint linenums">
$('#toggle-state-switch').bootstrapSwitch('status'); // true || false
$('#toggle-state-switch').bootstrapSwitch('toggleState');
$('#toggle-state-switch').bootstrapSwitch('setState', false); // true || false</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="destroy" class="anchor" href="#destroy">Destroy <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div id="destroy-switch" class="make-switch">
                    <input type="checkbox" checked>
                </div>

                <br />
                <br />

                <button id="btn-destroy-switch" class="btn">Destroy me!</button>
            </div>
            <pre class="prettyprint linenums">
$('#destroy-switch').bootstrapSwitch('destroy');</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="create" class="anchor" href="#create">Create <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <input id="create-switch" type="checkbox" checked>

                <br />
                <br />

                <div id="btn-create" class="btn">Create</div>
            </div>
            <pre class="prettyprint linenums">
$('#create-switch').wrap('&lt;div class="make-switch" />').parent().bootstrapSwitch();</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="disabled-javascript" class="anchor" href="#disabled-javascript">Disabled <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <label for="disable-switch-input">amazing label</label>
                <div id="disable-switch" class="make-switch">
                    <input id="disable-switch-input" type="checkbox" checked>
                </div>

                <br />
                <br />

                <button id="btn-is-active-switch" class="btn">Is active?</button>
                <button id="btn-toggle-activation-switch" class="btn">Toggle activation!</button>
                <button id="btn-disable-switch" class="btn">Disable!</button>
                <button id="btn-activate-switch" class="btn">Activate!</button>
            </div>
            <pre class="prettyprint linenums">
$('#disable-switch').bootstrapSwitch('isActive');
$('#disable-switch').bootstrapSwitch('toggleActivation');
$('#disable-switch').bootstrapSwitch('setActive', false);  // true || false</pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="radio-javascript" class="anchor" href="#radio-javascript">Radio <small>javascript</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="control-group">
                     <div class="controls">
                        <label for="option1">Option 1</label>
                        <div class="make-switch radio1 radio-no-uncheck">
                            <input id="option1" type="radio" name="radio1" value="option1">
                        </div>
                        <label for="option2">Option 2</label>
                        <div class="make-switch radio1 radio-no-uncheck">
                            <input id="option2" type="radio" name="radio1" value="option2">
                        </div>
                        <label for="option3">Option 3</label>
                        <div class="make-switch radio1 radio-no-uncheck">
                            <input id="option3" type="radio" name="radio1" value="option3">
                        </div>
                     </div>
                </div>
            </div>
            <pre class="prettyprint linenums">
&lt;div class="control-group">
    &lt;div class="controls">
        &lt;label for="option1">Option 1&lt;/label>
        &lt;div class="make-switch radio1 radio-no-uncheck">
            &lt;input id="option1" type="radio" name="radio1" value="option1">
        &lt;/div>
        &lt;label for="option2">Option 2&lt;/label>
        &lt;div class="make-switch radio1 radio-no-uncheck">
            &lt;input id="option2" type="radio" name="radio1" value="option2">
        &lt;/div>
        &lt;label for="option3">Option 3&lt;/label>
        &lt;div class="make-switch radio1 radio-no-uncheck">
            &lt;input id="option3" type="radio" name="radio1" value="option3">
        &lt;/div>
    &lt;/div>
&lt;/div>            
&lt;script>
    $('.radio1').on('switch-change', function () {
        $('.radio1').bootstrapSwitch('toggleRadioState');
    });
    // or
    $('.radio1').on('switch-change', function () {
        $('.radio1').bootstrapSwitch('toggleRadioStateAllowUncheck');
    });
    // or
    $('.radio1').on('switch-change', function () {
        $('.radio1').bootstrapSwitch('toggleRadioStateAllowUncheck', false);
    });
&lt;/script></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="radio-javascript-allow-uncheck" class="anchor" href="#radio-javascript-allow-uncheck">Radio <small>javascript (allow radios uncheck)</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <div class="control-group">
                     <div class="controls">
                        <label for="option11">Option 1</label>
                        <div class="make-switch radio2">
                            <input id="option11" type="radio" name="radio2" value="option11">
                        </div>
                        <label for="option12">Option 2</label>
                        <div class="make-switch radio2">
                            <input id="option12" type="radio" name="radio2" value="option12" checked="checked">
                        </div>
                        <label for="option13">Option 3</label>
                        <div class="make-switch radio2">
                            <input id="option13" type="radio" name="radio2" value="option13">
                        </div>
                     </div>
                </div>
            </div>
            <pre class="prettyprint linenums">
&lt;div class="control-group">
    &lt;div class="controls">
        &lt;label for="option11">Option 1&lt;/label>
        &lt;div class="make-switch radio2">
            &lt;input id="option11" type="radio" name="radio2" value="option1">
        &lt;/div>
        &lt;label for="option12">Option 2&lt;/label>
        &lt;div class="make-switch radio2">
            &lt;input id="option12" type="radio" name="radio2" value="option2" checked="checked">
        &lt;/div>
        &lt;label for="option13">Option 3&lt;/label>
        &lt;div class="make-switch radio2">
            &lt;input id="option13" type="radio" name="radio2" value="option3">
        &lt;/div>
    &lt;/div>
&lt;/div>            
&lt;script>
    $('.radio2').on('switch-change', function () {
        $('.radio2').bootstrapSwitch('toggleRadioStateAllowUncheck', true);
    });
&lt;/script></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="form" class="anchor" href="#form">Form <small>- try to use tab, space and reset button</small></a>
        </h2>
    </div>

    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <form class="form-horizontal span8 offset2">
                    <div class="control-group">
                        <label class="control-label" for="inputEmail">Email</label>
                        <div class="controls">
                            <input type="text" id="inputEmail" placeholder="Email">
                        </div>
                    </div>

                    <div class="control-group">
                        <label class="control-label" for="notification1">Notification 1</label>

                        <div class="controls">
                            <div class="make-switch" tabindex="0">
                                <input id="notification1" type="checkbox">
                            </div>
                        </div>
                    </div>
                    <div class="control-group">
                        <label class="control-label" for="notification2">Notification 2</label>

                        <div class="controls">
                            <div class="make-switch" tabindex="0">
                                <input id="notification2" type="checkbox">
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="reset" class="btn btn-inverse">Reset</button>
                    </div>
                </form>
                <div class="clearfix"></div>
            </div>
            
            <pre class="prettyprint linenums">
&lt;form class="form-horizontal">
    &lt;div class="control-group">
        &lt;label class="control-label" for="inputEmail">Email&lt;/label>
        &lt;div class="controls">
            &lt;input type="text" id="inputEmail" placeholder="Email">
        &lt;/div>
    &lt;/div>
    &lt;div class="control-group">
        &lt;label class="control-label" for="notification1">Notification 1&lt;/label>
        &lt;div class="controls">
            &lt;div class="make-switch" tabindex="0">
                &lt;input id="notification1" type="checkbox">
            &lt;/div>
        &lt;/div>
    &lt;/div>
    &lt;div class="control-group">
        &lt;label class="control-label" for="notification2">Notification 2&lt;/label>
        &lt;div class="controls">
            &lt;div class="make-switch" tabindex="0">
                &lt;input id="notification2" type="checkbox">
            &lt;/div>
        &lt;/div>
    &lt;/div>
    &lt;div class="form-actions">
        &lt;button type="reset" class="btn btn-inverse">Reset&lt;/button>
    &lt;/div>
&lt;/form></pre>
        </div>
    </div>

    <div class="row-fluid">
        <h2 class="span8 offset2">
            <a name="modal" class="anchor" href="#modal">Modal</a>
        </h2>
    </div>
        <!-- MODAL -->
    <div class="row-fluid">
        <div class="span8 offset2">
            <div class="bs-docs-example">
                <a href="#myModal" role="button" class="btn" data-toggle="modal">Modal</a>
            </div>
            <pre class="prettyprint linenums">
    &lt;a href="#myModal" role="button" class="btn" data-toggle="modal">Modal&lt;/a>

    &lt;div class="modal-body">
        &lt;div class="make-switch">
            &lt;input type="checkbox" checked>
        &lt;/div>
    &lt;/div></pre>
        </div>
    </div>
</div>

<div id="myModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-hidden="true">×</button>
        <h3>Modal</h3>
    </div>
    <div class="modal-body">
        <div class="make-switch">
            <input type="checkbox" checked>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn" data-dismiss="modal" aria-hidden="true">Close</button>
    </div>
</div>

<script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>

<script>
    $(document).ready(function() {
        window.prettyPrint && prettyPrint();
        $('#mySwitch').on('switch-change', function (e, data) {
            var $el = $(data.el)
                , value = data.value;
            console.log(e, $el, value);
        });

        // DIMENSION
        $('#btn-size-regular-switch').on('click', function () {
            $('#dimension-switch').bootstrapSwitch('setSizeClass', '');
        });
        $('#btn-size-mini-switch').on('click', function () {
            $('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-mini');
        });
        $('#btn-size-small-switch').on('click', function () {
            $('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-small');
        });
        $('#btn-size-large-switch').on('click', function () {
            $('#dimension-switch').bootstrapSwitch('setSizeClass', 'switch-large');
        });

        // STATE
        $('#toggle-state-switch-button').on('click', function () {
            $('#toggle-state-switch').bootstrapSwitch('toggleState');
        });
        $('#toggle-state-switch-button-on').on('click', function () {
            $('#toggle-state-switch').bootstrapSwitch('setState', true);
        });
        $('#toggle-state-switch-button-off').on('click', function () {
            $('#toggle-state-switch').bootstrapSwitch('setState', false);
        });
        $('#toggle-state-switch-button-status').on('click', function () {
            alert($('#toggle-state-switch').bootstrapSwitch('status'));
        });

        // DESTROY
        $('#btn-destroy-switch').on('click', function () {
            $('#destroy-switch').bootstrapSwitch('destroy');
            $(this).remove();
        });
        // CREATE
        $('#btn-create').on('click', function () {
            $('#create-switch').wrap('<div class="make-switch" />').parent().bootstrapSwitch();
            $(this).remove()
        });

        // ACTIVATION
        $('#btn-is-active-switch').on('click', function () {
            alert($('#disable-switch').bootstrapSwitch('isActive'));
        });
        $('#btn-toggle-activation-switch').on('click', function () {
            $('#disable-switch').bootstrapSwitch('toggleActivation');
        });
        $('#btn-disable-switch').on('click', function () {
            $('#disable-switch').bootstrapSwitch('setActive', false);
        });
        $('#btn-activate-switch').on('click', function () {
            $('#disable-switch').bootstrapSwitch('setActive', true);
        });

        // LABEL
        $('#btn-label-on-switch').on('click', function() {
            $('#label-switch').bootstrapSwitch('setOnLabel', 'I');
        });
        $('#btn-label-off-switch').on('click', function() {
            $('#label-switch').bootstrapSwitch('setOffLabel', 'O');
        });

        $('#label-toggle-switch').on('click', function(e, data) {
            $('.label-toggle-switch').bootstrapSwitch('toggleState');
        });
        $('.label-toggle-switch').on('switch-change', function(e, data) {
            alert(data.value);
        });
        $('#label2-toggle-switch').on('switch-change', function(e, data) {
            alert(data.value);
        });

        // COLOR
        $('#btn-color-on-switch').on('click', function() {
            $('#change-color-switch').bootstrapSwitch('setOnClass', 'success');
        });
        $('#btn-color-off-switch').on('click', function() {
            $('#change-color-switch').bootstrapSwitch('setOffClass', 'danger');
        });

        // ANIMATION
        $('#btn-animate-switch').on('click', function() {
            $('#animated-switch').bootstrapSwitch('setAnimated', true);
        });
        $('#btn-dont-animate-switch').on('click', function() {
            $('#animated-switch').bootstrapSwitch('setAnimated', false);
        });

        // RADIO
        $('.radio1').on('switch-change', function () {
            $('.radio1').bootstrapSwitch('toggleRadioState');
        });
        $('.radio2').on('switch-change', function () {
            $('.radio2').bootstrapSwitch('toggleRadioStateAllowUncheck', true);
        });
    });
</script>

    <footer>
        <div class="container-fluid">
            <p style="float:left; padding-top: 5px;">
                &copy; by
                <a href="http://larentis.eu" target="_blank">Mattia Larentis</a> (<a href="https://twitter.com/SpiritualGuru">@SpiritualGuru</a>)
                and
                <a href="http://www.bdmdesign.org/" target="_blank">Peter Stein</a>
            </p>
        </div>
    </footer>

    <script src="http://ajax.googleapis.com/ajax/libs/jquery/1.9.1/jquery.min.js"></script>
    <script src="http://getbootstrap.com/2.3.2/assets/js/google-code-prettify/prettify.js"></script>
    <script src="http://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/2.3.2/js/bootstrap.min.js"></script>
    <script src="../static/js/bootstrap-switch.js"></script>
</body>
</html>
