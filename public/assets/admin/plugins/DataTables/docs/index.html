<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
	<head>
		<meta http-equiv="Content-type" content="text/html; charset=utf-8">
		<title>Table of Contents - documentation</title>
  
		<style type="text/css" media="screen">
			@import "media/css/doc.css";
			@import "media/css/shCore.css";
			@import "media/css/shThemeDataTables.css";
		</style>
	
		<script type="text/javascript" src="media/js/shCore.js"></script>
		<script type="text/javascript" src="media/js/shBrushJScript.js"></script>
		<script type="text/javascript" src="media/js/jquery.js"></script>
		<script type="text/javascript" src="media/js/doc.js"></script>
	</head>
	<body>
		<div class="fw_container">
			<a name="top"></a>
			<div class="fw_header">
			</div>

			<div class="fw_content">
				<h3 class="subsection-title">Table of Contents</h3>
				<dl>
					<dt><a href="DataTable.html">DataTable</a></dt><dd><p>DataTables is a plug-in for the jQuery Javascript library. It is a 
highly flexible tool, based upon the foundations of progressive 
enhancement, which will add advanced interaction controls to any 
HTML table. For a full list of features please refer to
<a href="http://datatables.net">DataTables.net</a>.</p>

<p>Note that the <i>DataTable</i> object is not a global variable but is
aliased to <i>jQuery.fn.DataTable</i> and <i>jQuery.fn.dataTable</i> through which 
it may be  accessed.</p></dd>
				</dl>
   			</div>
		</div>

		<div class="fw_footer">
			DataTables: Copyright 2008-2012 Allan Jardine, all rights reserved<br>

			Documentation generated by <a href="https://github.com/micmath/JSDoc">JSDoc 3</a> on
			23th Sep 2012 - 14:27
			with the <a href="http://datatables.net/">DataTables</a> template.
		</div>
	</body>
</html>