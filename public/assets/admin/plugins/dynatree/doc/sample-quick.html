<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
<head>
	<meta http-equiv="content-type" content="text/html; charset=ISO-8859-1">
	<title>Dynatree - Example</title>

	<script src="../jquery/jquery.js" type="text/javascript"></script>
	<script src="../jquery/jquery-ui.custom.js" type="text/javascript"></script>
	<script src="../jquery/jquery.cookie.js" type="text/javascript"></script>

	<link href="../src/skin/ui.dynatree.css" rel="stylesheet" type="text/css">
	<script src="../src/jquery.dynatree.js" type="text/javascript"></script>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<link href="prettify.css" rel="stylesheet">
	<script src="prettify.js" type="text/javascript"></script>
	<link href="sample.css" rel="stylesheet" type="text/css">
	<script src="sample.js" type="text/javascript"></script>
	<!-- End_Exclude -->

	<!-- Add code to initialize the tree when the document is loaded: -->
	<script type="text/javascript">
	$(function(){
		// Attach the dynatree widget to an existing <div id="tree"> element
		// and pass the tree options as an argument to the dynatree() function:
		$("#tree").dynatree({
			onActivate: function(node) {
				// A DynaTreeNode object is passed to the activation handler
				// Note: we also get this event, if persistence is on, and the page is reloaded.
				alert("You activated " + node.data.title);
			},
			children: [
				{title: "Item 1"},
				{title: "Folder 2", isFolder: true, key: "folder2",
					children: [
						{title: "Sub-item 2.1"},
						{title: "Sub-item 2.2"}
					]
				},
				{title: "Item 3"}
			]
		});
	});
	</script>
</head>
<body class="example">
	<h1>Example: Quick start</h1>
	<p class="description">
		This sample initializes the tree from a JavaScript object.<br>
		An <i>onActivate</i> handler brings up an alert box when a node is selected.
	</p>

	<!-- Add a <div> element where the tree should appear: -->
	<div id="tree"> </div>

	<!-- Start_Exclude: This block is not part of the sample code -->
	<hr>
	<p class="sample-links  no_code">
		<a class="hideInsideFS" href="http://dynatree.googlecode.com">jquery.dynatree.js project home</a>
		<a class="hideOutsideFS" href="#">Link to this page</a>
		<a class="hideInsideFS" href="samples.html">Example Browser</a>
		<a href="#" id="codeExample">View source code</a>
	</p>
	<pre id="sourceCode" class="prettyprint" style="display:none"></pre>
	<!-- End_Exclude -->
</body>
</html>
